import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/auth/auth_service.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/utils/logger.dart';

/// Provider for the auth service
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// Provider for the current Firebase user
/// This provider listens to idTokenChanges() to detect authentication state changes
/// including when the app is restarted
final firebaseUserProvider = StreamProvider<firebase_auth.User?>((ref) {
  final authService = ref.watch(authServiceProvider);

  // Log the current user state when the provider is first created
  final currentUser = authService.currentUser;
  if (currentUser != null) {
    Logger.debug('firebaseUserProvider: Current user is ${currentUser.email}');
  } else {
    Logger.debug('firebaseUserProvider: No current user');
  }

  // Return the stream of auth state changes
  return authService.authStateChanges;
});

/// Provider to check if user is signed in
/// This provider depends on firebaseUserProvider and will update
/// whenever the authentication state changes
final isSignedInProvider = Provider<bool>((ref) {
  final userAsync = ref.watch(firebaseUserProvider);

  // Handle the different states of the async value
  return userAsync.when(
    // When we have data, check if the user is not null
    data: (user) {
      final isSignedIn = user != null;
      Logger.debug('isSignedInProvider: User is ${isSignedIn ? 'signed in' : 'signed out'}');
      return isSignedIn;
    },
    // When loading, check the current user directly as a fallback
    loading: () {
      final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
      final isSignedIn = currentUser != null;
      Logger.debug('isSignedInProvider (loading): User is ${isSignedIn ? 'signed in' : 'signed out'}');
      return isSignedIn;
    },
    // When there's an error, assume the user is not signed in
    error: (error, stackTrace) {
      Logger.error('isSignedInProvider: Error checking auth state', error);
      return false;
    },
  );
});

/// Provider for safe sign out with listener cleanup
/// This provider ensures all Firestore listeners are cleared BEFORE signing out
/// to prevent permission errors
final signOutProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    try {
      Logger.debug('Starting safe sign out with listener cleanup');

      // Step 1: Clear all Firestore listeners BEFORE signing out
      final listenerPool = ref.read(firestoreListenerPoolProvider);
      Logger.debug('Proactively clearing all Firestore listeners before sign out');
      listenerPool.clearAllListeners();

      // Step 2: Wait a brief moment to ensure listeners are fully cleared
      await Future.delayed(const Duration(milliseconds: 200));

      // Step 3: Now perform the actual sign out
      final authService = ref.read(authServiceProvider);
      await authService.signOut();

      Logger.debug('Safe sign out completed successfully');
    } catch (e) {
      Logger.error('Error during safe sign out', e);
      rethrow;
    }
  };
});
